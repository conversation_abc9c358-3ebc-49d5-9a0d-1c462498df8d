{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\code\\flutter\\blahh\\android\\app\\.cxx\\Debug\\706mp3d6\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\code\\flutter\\blahh\\android\\app\\.cxx\\Debug\\706mp3d6\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}