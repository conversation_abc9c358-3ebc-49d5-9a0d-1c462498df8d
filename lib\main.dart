import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';

void main() {
  runApp(const TapCircleGame());
}

class TapCircleGame extends StatelessWidget {
  const TapCircleGame({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Tap the Circle Game',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const GameScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with TickerProviderStateMixin {
  int score = 0;
  int timeLeft = 30;
  bool gameActive = false;
  bool gameStarted = false;

  // Circle properties
  double circleX = 0;
  double circleY = 0;
  Color circleColor = Colors.red;
  bool showCircle = false;

  Timer? gameTimer;
  Timer? circleTimer;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  final Random random = Random();
  final List<Color> circleColors = [
    Colors.red,
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.pink,
    Colors.teal,
    Colors.amber,
  ];

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    circleTimer?.cancel();
    _scaleController.dispose();
    super.dispose();
  }

  void startGame() {
    setState(() {
      score = 0;
      timeLeft = 30;
      gameActive = true;
      gameStarted = true;
      showCircle = false;
    });

    // Start countdown timer
    gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        timeLeft--;
      });

      if (timeLeft <= 0) {
        endGame();
      }
    });

    // Start spawning circles
    spawnCircle();
  }

  void endGame() {
    gameTimer?.cancel();
    circleTimer?.cancel();
    setState(() {
      gameActive = false;
      showCircle = false;
    });
  }

  void spawnCircle() {
    if (!gameActive) return;

    circleTimer = Timer(Duration(milliseconds: 800 + random.nextInt(1200)), () {
      if (gameActive) {
        setState(() {
          // Get screen size for positioning
          final size = MediaQuery.of(context).size;
          circleX = 50 + random.nextDouble() * (size.width - 150);
          circleY = 100 + random.nextDouble() * (size.height - 300);
          circleColor = circleColors[random.nextInt(circleColors.length)];
          showCircle = true;
        });

        // Hide circle after 2 seconds if not tapped
        Timer(const Duration(seconds: 2), () {
          if (showCircle && gameActive) {
            setState(() {
              showCircle = false;
            });
            spawnCircle(); // Spawn next circle
          }
        });
      }
    });
  }

  void tapCircle() {
    if (!showCircle || !gameActive) return;

    setState(() {
      score++;
      showCircle = false;
    });

    // Trigger scale animation
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });

    // Spawn next circle
    spawnCircle();
  }

  void resetGame() {
    setState(() {
      gameStarted = false;
      gameActive = false;
      showCircle = false;
      score = 0;
      timeLeft = 30;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black87,
      body: SafeArea(
        child: Stack(
          children: [
            // Background gradient
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.indigo, Colors.black87],
                ),
              ),
            ),

            // Game UI
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Score
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'Score: $score',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  // Timer
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color:
                          timeLeft <= 10
                              ? Colors.red.withValues(alpha: 0.3)
                              : Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'Time: $timeLeft',
                      style: TextStyle(
                        color: timeLeft <= 10 ? Colors.red : Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Game area
            if (!gameStarted)
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.games, size: 80, color: Colors.white),
                    const SizedBox(height: 20),
                    const Text(
                      'Tap the Circle Game',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    const Text(
                      'Tap the circles as fast as you can!\nYou have 30 seconds.',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.white70, fontSize: 16),
                    ),
                    const SizedBox(height: 40),
                    ElevatedButton(
                      onPressed: startGame,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 40,
                          vertical: 15,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                      child: const Text(
                        'Start Game',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Game over screen
            if (gameStarted && !gameActive)
              Center(
                child: Container(
                  padding: const EdgeInsets.all(30),
                  margin: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.emoji_events,
                        size: 60,
                        color: Colors.amber,
                      ),
                      const SizedBox(height: 20),
                      const Text(
                        'Game Over!',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        'Final Score: $score',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        score >= 20
                            ? 'Excellent!'
                            : score >= 15
                            ? 'Great job!'
                            : score >= 10
                            ? 'Good work!'
                            : 'Keep practicing!',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 30),
                      ElevatedButton(
                        onPressed: resetGame,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 30,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        child: const Text(
                          'Play Again',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Circle to tap
            if (showCircle && gameActive)
              Positioned(
                left: circleX,
                top: circleY,
                child: GestureDetector(
                  onTap: tapCircle,
                  child: AnimatedBuilder(
                    animation: _scaleAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _scaleAnimation.value,
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: circleColor,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: circleColor.withOpacity(0.5),
                                blurRadius: 10,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.touch_app,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
